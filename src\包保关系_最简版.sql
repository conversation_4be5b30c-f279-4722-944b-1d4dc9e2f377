CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer'='DROP';
SET
  'table.exec.sink.upsert-materialize'='NONE';

CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_huafeiquan_new` (
    `serial_number` STRING,
    `user_id` STRING,
    `province_code` STRING,
    `ACTIVATE_FLAG` BOOLEAN,
    CONSTRAINT `pk` PRIMARY KEY (`serial_number`) NOT ENFORCED
) WITH (
  'bucket' = '128',
  'bucket-key' = 'serial_number',
  'file.format' = 'avro',
  'merge-engine' = 'partial-update',
  'changelog-producer' = 'lookup',
  'write-buffer-size' = '256MB',
  'write-only' = 'true'
);

CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_huafeiquan_hbase` (
    `serial_number` STRING,
    `ACTIVATE_FLAG` BOOLEAN
) WITH (
  'connector' = 'upsert-kafka',
  'topic' = 'IN_KAFKA_TO_HBASE',
  'properties.bootstrap.servers' = '10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092,10.177.25.76:9092',
  'key.format' = 'json',
  'key.fields' = 'serial_number',
  'value.format' = 'json',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'PLAIN',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.plain.PlainLoginModule required username="context" password="context";'
);

CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_huafeiquan_quanke_new` (
  `serial_number` STRING,
  `ACTIVATE_FLAG` BOOLEAN
)
WITH (
  'connector' = 'upsert-kafka',
  'properties.bootstrap.servers' = '10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092',
  'topic' = 'ZT_STRATEGY_9900_DTS01',
  'properties.session.timeout.ms' = '300000',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'PLAIN',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.plain.PlainLoginModule required username="context" password="context";',
  'key.format' = 'json',
  'key.fields' = 'serial_number',
  'value.format' = 'json',
  'key.json.ignore-parse-errors' = 'true',
  'key.json.fail-on-missing-field' = 'false',
  'value.json.ignore-parse-errors' = 'true',
  'value.json.fail-on-missing-field' = 'false'
  -- 'format' = 'csv',
  -- 'csv.ignore-parse-errors' = 'true',
  -- 'csv.field-delimiter' = '\u0001'
);

INSERT INTO `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_huafeiquan_quanke_new`
SELECT
  serial_number,
  ACTIVATE_FLAG
FROM
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_huafeiquan_new`
  /*+ OPTIONS('properties.group.id'='ods_r_kafka_huafeiquan_20240508','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;

INSERT INTO `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_huafeiquan_hbase`
SELECT
  serial_number,
  ACTIVATE_FLAG
FROM
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_huafeiquan_new`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_huafeiquan_hbase_20240508','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;